/*
  ! X-Frame-Options
  Content-Security-Policy: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; frame-ancestors *; frame-src *; child-src *; connect-src *; font-src *; img-src *; media-src *; object-src *; script-src 'self' 'unsafe-inline' 'unsafe-eval' *; style-src 'self' 'unsafe-inline' *;
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With
