/* Messages Component - Custom CSS to avoid Tailwind conflicts */

.messages-container {
  width: 100%;
  height: 500px;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.messages-container.dark {
  background-color: #1f2937;
  border: 1px solid #374151;
}

.messages-container.light {
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
}

/* Header */
.messages-header {
  padding: 16px 20px;
  border-bottom: 1px solid;
  flex-shrink: 0;
}

.messages-container.dark .messages-header {
  background-color: #111827;
  border-bottom-color: #374151;
}

.messages-container.light .messages-header {
  background-color: #f9fafb;
  border-bottom-color: #e5e7eb;
}

.messages-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

.messages-container.dark .messages-title {
  color: #3b82f6;
}

.messages-container.light .messages-title {
  color: #1d4ed8;
}

.messages-icon {
  font-size: 20px;
}

/* Messages List */
.messages-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.messages-list::-webkit-scrollbar {
  width: 6px;
}

.messages-list::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container.dark .messages-list::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 3px;
}

.messages-container.light .messages-list::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

/* Message Wrapper */
.message-wrapper {
  display: flex;
  width: 100%;
  margin-bottom: 4px;
}

.message-wrapper.customer {
  justify-content: flex-start;
}

.message-wrapper.agent {
  justify-content: flex-end;
}

/* Message Bubble */
.message-bubble {
  max-width: 75%;
  min-width: 200px;
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Customer Messages (Left side) */
.message-wrapper.customer .message-bubble {
  background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%);
  border-bottom-left-radius: 6px;
  margin-right: 60px;
}

.messages-container.dark .message-wrapper.customer .message-bubble {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  color: #f9fafb;
}

.messages-container.light .message-wrapper.customer .message-bubble {
  color: #111827;
}

/* Agent Messages (Right side) */
.message-wrapper.agent .message-bubble {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border-bottom-right-radius: 6px;
  margin-left: 60px;
}

/* Message Header */
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  opacity: 0.8;
}

.sender-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.sender-icon {
  font-size: 14px;
}

.sender-name {
  font-weight: 600;
  font-size: 12px;
}

.message-timestamp {
  font-size: 11px;
  opacity: 0.7;
}

/* Message Content */
.message-content {
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-line {
  margin-bottom: 4px;
}

.message-line:last-child {
  margin-bottom: 0;
}

/* Arabic text support */
.message-content {
  direction: auto;
  text-align: start;
}

/* Message Meta */
.message-meta {
  margin-top: 8px;
  font-size: 10px;
  opacity: 0.6;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 6px;
}

.message-wrapper.customer .message-meta {
  border-top-color: rgba(0, 0, 0, 0.1);
}

/* Footer */
.messages-footer {
  padding: 12px 20px;
  border-top: 1px solid;
  flex-shrink: 0;
}

.messages-container.dark .messages-footer {
  background-color: #111827;
  border-top-color: #374151;
}

.messages-container.light .messages-footer {
  background-color: #f9fafb;
  border-top-color: #e5e7eb;
}

.conversation-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.messages-container.dark .conversation-status {
  color: #9ca3af;
}

.messages-container.light .conversation-status {
  color: #6b7280;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* No Messages State */
.no-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
}

.no-messages-icon {
  font-size: 48px;
  opacity: 0.5;
}

.no-messages-text {
  font-size: 16px;
  opacity: 0.7;
}

.messages-container.dark .no-messages-text {
  color: #9ca3af;
}

.messages-container.light .no-messages-text {
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 85%;
    min-width: 150px;
  }
  
  .message-wrapper.customer .message-bubble {
    margin-right: 30px;
  }
  
  .message-wrapper.agent .message-bubble {
    margin-left: 30px;
  }
  
  .messages-container {
    height: 400px;
  }
}

@media (max-width: 480px) {
  .message-bubble {
    max-width: 90%;
    min-width: 120px;
    padding: 10px 12px;
  }
  
  .message-wrapper.customer .message-bubble {
    margin-right: 20px;
  }
  
  .message-wrapper.agent .message-bubble {
    margin-left: 20px;
  }
  
  .messages-list {
    padding: 12px;
  }
  
  .messages-header,
  .messages-footer {
    padding: 12px 16px;
  }
}
