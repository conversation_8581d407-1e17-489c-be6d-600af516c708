<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatwoot Helper - Iframe Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .iframe-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: 20px;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Chatwoot Helper - Iframe Embedding Test</h1>
        
        <div class="info">
            <h3>📋 Test Information</h3>
            <p><strong>Purpose:</strong> This page tests whether the Chatwoot Helper React app can be successfully embedded in an iframe without X-Frame-Options errors.</p>
            <p><strong>Expected Result:</strong> The app should load normally below, showing "Waiting for Chatwoot SDK..." message.</p>
            <p><strong>Server:</strong> http://localhost:3000</p>
        </div>

        <div class="iframe-container">
            <h3>📱 Embedded Chatwoot Helper App</h3>
            <iframe 
                src="http://localhost:3000" 
                title="Chatwoot Helper App"
                frameborder="0"
                allowfullscreen>
                <p>Your browser does not support iframes. Please visit <a href="http://localhost:3000">http://localhost:3000</a> directly.</p>
            </iframe>
        </div>

        <div class="info">
            <h3>✅ Success Indicators</h3>
            <ul>
                <li>The iframe loads without "Refused to display in a frame" errors</li>
                <li>You can see the Chatwoot Helper interface with the loading spinner</li>
                <li>The app displays "Waiting for Chatwoot SDK..." message</li>
                <li>No console errors related to X-Frame-Options</li>
            </ul>
        </div>

        <div class="info error">
            <h3>❌ If You See Errors</h3>
            <ul>
                <li>Check browser console for X-Frame-Options errors</li>
                <li>Ensure the server is running on port 3000</li>
                <li>Verify the server.js configuration includes proper headers</li>
                <li>Try refreshing the page</li>
            </ul>
        </div>
    </div>

    <script>
        // Monitor iframe loading
        const iframe = document.querySelector('iframe');
        iframe.onload = function() {
            console.log('✅ Iframe loaded successfully!');
        };
        iframe.onerror = function() {
            console.error('❌ Iframe failed to load');
        };
    </script>
</body>
</html>
