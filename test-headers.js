import http from 'http';

function testHeaders() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log('\n🔍 Testing Chatwoot Helper Headers');
    console.log('=====================================');
    console.log(`Status: ${res.statusCode} ${res.statusMessage}`);
    console.log('\n📋 All Response Headers:');
    console.log(res.headers);
    
    console.log('\n🎯 Key Headers for Iframe Embedding:');
    
    // Check X-Frame-Options
    const xFrameOptions = res.headers['x-frame-options'];
    if (xFrameOptions) {
      console.log(`❌ X-Frame-Options: ${xFrameOptions} (This will block iframe embedding)`);
    } else {
      console.log('✅ X-Frame-Options: Not set (Good for iframe embedding)');
    }
    
    // Check Content-Security-Policy
    const csp = res.headers['content-security-policy'];
    if (csp) {
      if (csp.includes('frame-ancestors *')) {
        console.log('✅ Content-Security-Policy: Allows iframe embedding from any origin');
      } else {
        console.log(`⚠️ Content-Security-Policy: ${csp.substring(0, 100)}...`);
      }
    } else {
      console.log('⚠️ Content-Security-Policy: Not set');
    }
    
    // Check CORS
    const cors = res.headers['access-control-allow-origin'];
    if (cors === '*') {
      console.log('✅ Access-Control-Allow-Origin: * (Allows all origins)');
    } else if (cors) {
      console.log(`⚠️ Access-Control-Allow-Origin: ${cors}`);
    } else {
      console.log('⚠️ Access-Control-Allow-Origin: Not set');
    }
    
    console.log('\n🎉 Header Test Complete!');
    console.log('=====================================\n');
  });

  req.on('error', (e) => {
    console.error(`❌ Request failed: ${e.message}`);
    console.log('Make sure the server is running on port 3000');
  });

  req.end();
}

// Run the test
testHeaders();
