import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { iframePlugin } from "./vite-iframe-plugin.js";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), iframePlugin()],
  server: {
    port: 3000,
    host: true,
    open: true,
    middlewareMode: false,
    cors: {
      origin: "*",
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Authorization"],
    },
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  },
  preview: {
    port: 4173,
    host: true,
    cors: {
      origin: "*",
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowedHeaders: ["Content-Type", "Authorization"],
    },
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  },
  build: {
    outDir: "dist",
    sourcemap: true,
  },
});
