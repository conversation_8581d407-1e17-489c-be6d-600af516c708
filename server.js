import express from "express";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = process.env.PORT || 3000;

// Middleware to set headers for iframe embedding
app.use((req, res, next) => {
  // Completely remove X-Frame-Options header to allow iframe embedding
  res.removeHeader("X-Frame-Options");
  res.removeHeader("x-frame-options");

  // Don't set X-Frame-Options at all - this is the key fix
  // Setting it to ALLOWALL doesn't work in all browsers

  // Remove any restrictive CSP headers
  res.removeHeader("Content-Security-Policy");
  res.removeHeader("content-security-policy");

  // Set permissive CSP that allows iframe embedding from anywhere
  res.setHeader(
    "Content-Security-Policy",
    "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; " +
      "frame-ancestors *; " +
      "frame-src *; " +
      "child-src *; " +
      "connect-src *; " +
      "font-src *; " +
      "img-src *; " +
      "media-src *; " +
      "object-src *; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' *; " +
      "style-src 'self' 'unsafe-inline' *;"
  );

  // Enable CORS
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  res.setHeader(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, X-Requested-With"
  );
  res.setHeader("Access-Control-Allow-Credentials", "false");

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    res.status(200).end();
    return;
  }

  next();
});

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, "dist")));

// Handle React Router (return index.html for all routes)
app.get("*", (req, res) => {
  res.sendFile(path.join(__dirname, "dist", "index.html"));
});

app.listen(port, () => {
  console.log(`Server is running on port ${port}`);
  console.log(`Access the app at: http://localhost:${port}`);
});

export default app;
