<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Header Test - Chatwoot Helper</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .success {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border: 2px solid #f44336;
            color: #c62828;
        }
        .warning {
            background: #fff3e0;
            border: 2px solid #ff9800;
            color: #e65100;
        }
        .info {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            color: #1565c0;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 4px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Chatwoot Helper - Header & Iframe Test</h1>
        
        <div class="test-section info">
            <h2>📋 Test Overview</h2>
            <p>This page tests the HTTP headers and iframe embedding capabilities of the Chatwoot Helper app.</p>
            <p><strong>Server URL:</strong> http://localhost:3000</p>
        </div>

        <div class="test-section" id="header-test">
            <h2>🌐 HTTP Headers Test</h2>
            <p>Testing server headers...</p>
            <div id="header-results"></div>
        </div>

        <div class="test-section">
            <h2>📱 Iframe Embedding Test</h2>
            <p>Testing if the app can be embedded in an iframe:</p>
            <div id="iframe-test-result" class="test-result info">
                <strong>Loading iframe...</strong>
            </div>
            <iframe 
                id="test-iframe"
                src="http://localhost:3000" 
                title="Chatwoot Helper App"
                frameborder="0">
                <p>Your browser does not support iframes.</p>
            </iframe>
        </div>

        <div class="test-section">
            <h2>🔧 Troubleshooting</h2>
            <div class="warning">
                <h3>If iframe embedding fails:</h3>
                <ul>
                    <li>Check browser console for X-Frame-Options errors</li>
                    <li>Verify server is running on port 3000</li>
                    <li>Ensure server.js has proper header configuration</li>
                    <li>Try hard refresh (Ctrl+F5)</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Test HTTP headers
        async function testHeaders() {
            const resultsDiv = document.getElementById('header-results');
            
            try {
                const response = await fetch('http://localhost:3000', {
                    method: 'HEAD',
                    mode: 'cors'
                });
                
                const headers = {};
                for (let [key, value] of response.headers.entries()) {
                    headers[key] = value;
                }
                
                let headerHtml = '<h3>Response Headers:</h3><pre>' + JSON.stringify(headers, null, 2) + '</pre>';
                
                // Check specific headers
                const xFrameOptions = response.headers.get('x-frame-options');
                const csp = response.headers.get('content-security-policy');
                const cors = response.headers.get('access-control-allow-origin');
                
                headerHtml += '<h3>Key Headers Analysis:</h3>';
                
                if (xFrameOptions) {
                    headerHtml += '<div class="test-result error">❌ X-Frame-Options is set to: ' + xFrameOptions + ' (Should be removed for iframe embedding)</div>';
                } else {
                    headerHtml += '<div class="test-result success">✅ X-Frame-Options is not set (Good for iframe embedding)</div>';
                }
                
                if (csp && csp.includes('frame-ancestors *')) {
                    headerHtml += '<div class="test-result success">✅ Content-Security-Policy allows frame-ancestors from any origin</div>';
                } else if (csp) {
                    headerHtml += '<div class="test-result warning">⚠️ Content-Security-Policy present but may not allow iframe embedding: ' + csp.substring(0, 100) + '...</div>';
                } else {
                    headerHtml += '<div class="test-result warning">⚠️ No Content-Security-Policy header found</div>';
                }
                
                if (cors === '*') {
                    headerHtml += '<div class="test-result success">✅ CORS allows all origins</div>';
                } else {
                    headerHtml += '<div class="test-result warning">⚠️ CORS may be restrictive: ' + cors + '</div>';
                }
                
                resultsDiv.innerHTML = headerHtml;
                
            } catch (error) {
                resultsDiv.innerHTML = '<div class="test-result error">❌ Failed to fetch headers: ' + error.message + '</div>';
            }
        }
        
        // Test iframe loading
        const iframe = document.getElementById('test-iframe');
        const iframeResult = document.getElementById('iframe-test-result');
        
        iframe.onload = function() {
            iframeResult.className = 'test-result success';
            iframeResult.innerHTML = '✅ <strong>Iframe loaded successfully!</strong> The app should be visible below.';
            console.log('✅ Iframe loaded successfully!');
        };
        
        iframe.onerror = function() {
            iframeResult.className = 'test-result error';
            iframeResult.innerHTML = '❌ <strong>Iframe failed to load.</strong> Check console for errors.';
            console.error('❌ Iframe failed to load');
        };
        
        // Check for iframe blocking after a delay
        setTimeout(() => {
            try {
                // Try to access iframe content (will fail if blocked by X-Frame-Options)
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                if (!iframeDoc) {
                    iframeResult.className = 'test-result error';
                    iframeResult.innerHTML = '❌ <strong>Iframe blocked by security policy.</strong> Check X-Frame-Options and CSP headers.';
                }
            } catch (e) {
                // This is expected due to CORS, but iframe might still be working
                console.log('Cannot access iframe content (expected due to CORS)');
            }
        }, 3000);
        
        // Run header test on page load
        window.onload = function() {
            testHeaders();
        };
    </script>
</body>
</html>
