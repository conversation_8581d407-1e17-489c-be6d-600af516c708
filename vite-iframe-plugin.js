// Custom Vite plugin to handle iframe embedding headers
export function iframePlugin() {
  return {
    name: 'iframe-headers',
    configureServer(server) {
      server.middlewares.use((req, res, next) => {
        // Remove any existing X-Frame-Options headers
        res.removeHeader('X-Frame-Options');
        res.removeHeader('x-frame-options');
        
        // Remove any existing CSP headers
        res.removeHeader('Content-Security-Policy');
        res.removeHeader('content-security-policy');
        
        // Set permissive headers for iframe embedding
        res.setHeader('Content-Security-Policy', 
          "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; " +
          "frame-ancestors *; " +
          "frame-src *; " +
          "child-src *; " +
          "connect-src *; " +
          "font-src *; " +
          "img-src *; " +
          "media-src *; " +
          "object-src *; " +
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' *; " +
          "style-src 'self' 'unsafe-inline' *;"
        );
        
        // Enable CORS
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        res.setHeader('Access-Control-Allow-Credentials', 'false');
        
        // Handle preflight requests
        if (req.method === 'OPTIONS') {
          res.statusCode = 200;
          res.end();
          return;
        }
        
        next();
      });
    },
    configurePreviewServer(server) {
      server.middlewares.use((req, res, next) => {
        // Same configuration for preview server
        res.removeHeader('X-Frame-Options');
        res.removeHeader('x-frame-options');
        res.removeHeader('Content-Security-Policy');
        res.removeHeader('content-security-policy');
        
        res.setHeader('Content-Security-Policy', 
          "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; " +
          "frame-ancestors *; " +
          "frame-src *; " +
          "child-src *; " +
          "connect-src *; " +
          "font-src *; " +
          "img-src *; " +
          "media-src *; " +
          "object-src *; " +
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' *; " +
          "style-src 'self' 'unsafe-inline' *;"
        );
        
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        res.setHeader('Access-Control-Allow-Credentials', 'false');
        
        if (req.method === 'OPTIONS') {
          res.statusCode = 200;
          res.end();
          return;
        }
        
        next();
      });
    }
  };
}
