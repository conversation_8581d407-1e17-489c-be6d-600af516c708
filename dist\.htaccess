# Allow iframe embedding from any origin - REMOVE X-Frame-Options completely
Header always unset X-Frame-Options
Header always unset x-frame-options
# DO NOT set X-Frame-Options at all - this is key for iframe embedding

# Set permissive Content Security Policy
Header always unset Content-Security-Policy
Header always unset content-security-policy
Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; frame-ancestors *; frame-src *; child-src *; connect-src *; font-src *; img-src *; media-src *; object-src *; script-src 'self' 'unsafe-inline' 'unsafe-eval' *; style-src 'self' 'unsafe-inline' *;"

# Enable CORS for all origins
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"

# Handle preflight requests
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
