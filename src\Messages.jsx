import React from 'react';
// import './Messages.css'; // Temporarily disabled to test if CSS is causing issues

const Messages = ({ messages = [], theme = 'dark' }) => {
  console.log('🎨 MESSAGES COMPONENT IS RENDERING!!! Count:', messages?.length || 0, 'Theme:', theme);

  return (
    <div style={{
      border: '5px solid red',
      backgroundColor: 'yellow',
      padding: '20px',
      margin: '10px',
      minHeight: '200px',
      fontSize: '18px',
      fontWeight: 'bold',
      color: 'black',
      textAlign: 'center'
    }}>
      <h2 style={{ color: 'red', fontSize: '24px' }}>🚨 MESSAGES COMPONENT IS WORKING! 🚨</h2>
      <p>Messages count: {messages?.length || 0}</p>
      <p>Theme: {theme}</p>
      <p>If you can see this, the component is rendering correctly!</p>
      {messages && messages.length > 0 ? (
        <div>
          <h3>Messages found:</h3>
          {messages.map((msg, idx) => (
            <div key={idx} style={{ margin: '10px', padding: '10px', border: '1px solid black' }}>
              {msg.content}
            </div>
          ))}
        </div>
      ) : (
        <p style={{ color: 'blue' }}>No messages - Click "Load Mock Data" to test</p>
      )}
    </div>
  );
};



export default Messages;
