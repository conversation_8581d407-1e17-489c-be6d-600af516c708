import React from 'react';
import './Messages.css';

const Messages = ({ messages = [], theme = 'dark' }) => {
  if (!messages || messages.length === 0) {
    return (
      <div className={`messages-container ${theme}`}>
        <div className="no-messages">
          <div className="no-messages-icon">💬</div>
          <div className="no-messages-text">No messages in this conversation</div>
        </div>
      </div>
    );
  }

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const isCustomerMessage = (message) => {
    return message.message_type === 0 || message.sender?.type === 'contact';
  };

  const getMessageSender = (message) => {
    if (isCustomerMessage(message)) {
      return {
        name: message.sender?.name || 'Customer',
        type: 'customer',
        icon: '👤'
      };
    } else {
      return {
        name: message.sender?.name || 'Agent',
        type: 'agent',
        icon: '🤖'
      };
    }
  };

  const renderMessageContent = (content) => {
    if (!content) return '';
    
    // Handle long messages by adding line breaks for better readability
    const formattedContent = content.replace(/\. /g, '.\n');
    
    return formattedContent.split('\n').map((line, index) => (
      <div key={index} className="message-line">
        {line}
      </div>
    ));
  };

  return (
    <div className={`messages-container ${theme}`}>
      <div className="messages-header">
        <div className="messages-title">
          <span className="messages-icon">💬</span>
          Conversation Messages ({messages.length})
        </div>
      </div>
      
      <div className="messages-list">
        {messages.map((message, index) => {
          const sender = getMessageSender(message);
          const isCustomer = sender.type === 'customer';
          
          return (
            <div
              key={message.id || index}
              className={`message-wrapper ${isCustomer ? 'customer' : 'agent'}`}
            >
              <div className="message-bubble">
                <div className="message-header">
                  <div className="sender-info">
                    <span className="sender-icon">{sender.icon}</span>
                    <span className="sender-name">{sender.name}</span>
                  </div>
                  <div className="message-timestamp">
                    {formatTimestamp(message.created_at)}
                  </div>
                </div>
                
                <div className="message-content">
                  {renderMessageContent(message.content)}
                </div>
                
                {message.source_id && (
                  <div className="message-meta">
                    <span className="message-id">ID: {message.id}</span>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="messages-footer">
        <div className="conversation-status">
          <span className="status-indicator"></span>
          Conversation is active
        </div>
      </div>
    </div>
  );
};

export default Messages;
