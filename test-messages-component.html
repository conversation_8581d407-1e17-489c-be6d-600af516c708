<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages Component Test - Chatwoot Helper</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        .primary { background: #3b82f6; color: white; }
        .primary:hover { background: #2563eb; }
        .secondary { background: #6b7280; color: white; }
        .secondary:hover { background: #4b5563; }
        .success { background: #10b981; color: white; }
        .success:hover { background: #059669; }
        .warning { background: #f59e0b; color: white; }
        .warning:hover { background: #d97706; }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .iframe-container {
            background: #1f2937;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        iframe {
            width: 100%;
            height: 700px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .feature-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
        }
        .feature-item h4 {
            margin: 0 0 10px 0;
            color: #2563eb;
        }
        .feature-item ul {
            margin: 0;
            padding-left: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #10b981; }
        .status-warning { background-color: #f59e0b; }
        .status-error { background-color: #ef4444; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Messages Component Test Suite</h1>
        
        <div class="info">
            <h3>📋 Test Overview</h3>
            <p>This page tests the new custom Messages component with real Chatwoot mock data.</p>
            <p><strong>Features being tested:</strong> Chat interface, message bubbles, Arabic text support, timestamps, sender identification, scrolling, and theme support.</p>
        </div>

        <div class="test-section">
            <h2>🎮 Test Controls</h2>
            <div class="test-controls">
                <button class="success" onclick="loadMockData()">Load Mock Data</button>
                <button class="primary" onclick="toggleTheme()">Toggle Theme</button>
                <button class="secondary" onclick="testScrolling()">Test Scrolling</button>
                <button class="warning" onclick="testArabicText()">Test Arabic Text</button>
                <button class="secondary" onclick="clearMessages()">Clear Messages</button>
                <button class="primary" onclick="runFullTest()">Run Full Test</button>
            </div>
            
            <div class="test-results" id="testResults">
                <div><span class="status-indicator status-success"></span>Test suite ready. Click buttons above to run tests.</div>
            </div>
        </div>

        <div class="test-section">
            <h2>✅ Expected Features</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h4>🎨 Visual Design</h4>
                    <ul>
                        <li>Modern chat interface with message bubbles</li>
                        <li>Customer messages (left, gray/blue)</li>
                        <li>Agent messages (right, blue gradient)</li>
                        <li>Proper spacing and typography</li>
                        <li>Smooth scrolling with custom scrollbar</li>
                    </ul>
                </div>
                <div class="feature-item">
                    <h4>📱 Message Features</h4>
                    <ul>
                        <li>Sender identification (👤 Customer, 🤖 Agent)</li>
                        <li>Timestamp formatting</li>
                        <li>Message content with line breaks</li>
                        <li>Message ID display</li>
                        <li>Responsive design for mobile</li>
                    </ul>
                </div>
                <div class="feature-item">
                    <h4>🌍 Internationalization</h4>
                    <ul>
                        <li>Arabic text support (RTL)</li>
                        <li>Mixed language messages</li>
                        <li>Proper text direction handling</li>
                        <li>Unicode emoji support</li>
                    </ul>
                </div>
                <div class="feature-item">
                    <h4>🎯 Technical Features</h4>
                    <ul>
                        <li>Custom CSS (no Tailwind conflicts)</li>
                        <li>Theme support (light/dark)</li>
                        <li>Fixed height with scrolling</li>
                        <li>Real mock data integration</li>
                        <li>Performance optimized rendering</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="iframe-container">
            <h2 style="color: white; margin-top: 0;">📱 Live Messages Component Test</h2>
            <iframe 
                id="messagesApp"
                src="http://localhost:3000" 
                title="Chatwoot Helper with Messages Component">
                <p>Your browser does not support iframes.</p>
            </iframe>
        </div>
    </div>

    <script>
        const iframe = document.getElementById('messagesApp');
        const results = document.getElementById('testResults');
        let currentTheme = 'dark';

        function addTestResult(message, status = 'success') {
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = `status-${status}`;
            const entry = document.createElement('div');
            entry.innerHTML = `<span class="status-indicator ${statusClass}"></span>[${timestamp}] ${message}`;
            results.appendChild(entry);
            results.scrollTop = results.scrollHeight;
        }

        function sendMessageToApp(action, data = {}) {
            try {
                const message = { action, data, timestamp: Date.now() };
                iframe.contentWindow.postMessage(JSON.stringify(message), '*');
                addTestResult(`Sent ${action} command to app`);
            } catch (error) {
                addTestResult(`Error sending ${action}: ${error.message}`, 'error');
            }
        }

        function loadMockData() {
            addTestResult('Loading mock data with real Chatwoot conversation...', 'warning');
            
            // Send the appContext event with real mock data
            const mockAppContext = {
                event: "appContext",
                data: {
                    conversation: {
                        id: 9,
                        status: "open",
                        messages: [
                            {
                                id: 603,
                                content: "who?",
                                message_type: 0,
                                created_at: 1748778926,
                                sender: { name: "Ahmed Magdy", type: "contact" }
                            },
                            {
                                id: 621,
                                content: "Nice to meet you 😊",
                                message_type: 0,
                                created_at: 1748779551,
                                sender: { name: "Ahmed Magdy", type: "contact" }
                            },
                            {
                                id: 628,
                                content: "السلام عليكم",
                                message_type: 0,
                                created_at: 1748780636,
                                sender: { name: "Ahmed Magdy", type: "contact" }
                            },
                            {
                                id: 629,
                                content: "وعليكم السلام ورحمة الله وبركاته.",
                                message_type: 1,
                                created_at: 1748780638,
                                sender: { name: "Ahmed Magdy", type: "user" }
                            },
                            {
                                id: 630,
                                content: "مين معايا ؟",
                                message_type: 0,
                                created_at: 1748780645,
                                sender: { name: "Ahmed Magdy", type: "contact" }
                            },
                            {
                                id: 632,
                                content: "أنا نموذج لغوي كبير، دربتني جوجل.",
                                message_type: 1,
                                created_at: 1748780647,
                                sender: { name: "Ahmed Magdy", type: "user" }
                            }
                        ]
                    },
                    contact: {
                        id: 2,
                        name: "Ahmed Magdy",
                        phone_number: "+201128829358"
                    },
                    currentAgent: {
                        id: 1,
                        name: "Ahmed Magdy",
                        email: "<EMAIL>"
                    }
                }
            };
            
            iframe.contentWindow.postMessage(JSON.stringify(mockAppContext), '*');
            addTestResult('Mock data with Arabic/English messages sent successfully');
        }

        function toggleTheme() {
            currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
            sendMessageToApp('toggleTheme', { theme: currentTheme });
            addTestResult(`Switched to ${currentTheme} theme`);
        }

        function testScrolling() {
            addTestResult('Testing message scrolling behavior...', 'warning');
            sendMessageToApp('testScrolling');
        }

        function testArabicText() {
            addTestResult('Testing Arabic text rendering and RTL support...', 'warning');
            const arabicMessages = [
                { content: "مرحبا، كيف حالك؟", type: 'customer' },
                { content: "أهلا وسهلا! أنا بخير، شكرا لك", type: 'agent' },
                { content: "هل يمكنك مساعدتي في شيء؟", type: 'customer' }
            ];
            sendMessageToApp('testArabic', { messages: arabicMessages });
        }

        function clearMessages() {
            sendMessageToApp('clearMessages');
            addTestResult('Cleared all messages');
        }

        function runFullTest() {
            addTestResult('Starting comprehensive test suite...', 'warning');
            
            setTimeout(() => loadMockData(), 500);
            setTimeout(() => testScrolling(), 2000);
            setTimeout(() => testArabicText(), 3500);
            setTimeout(() => toggleTheme(), 5000);
            setTimeout(() => {
                addTestResult('Full test suite completed! ✅', 'success');
            }, 6000);
        }

        // Listen for messages from the iframe
        window.addEventListener('message', function(event) {
            try {
                const data = JSON.parse(event.data);
                if (data.type === 'test-result') {
                    addTestResult(data.message, data.status || 'success');
                }
            } catch (e) {
                // Ignore non-JSON messages
            }
        });

        // Auto-load mock data when iframe loads
        iframe.onload = function() {
            addTestResult('Messages component iframe loaded successfully');
            setTimeout(() => {
                addTestResult('Auto-loading mock data for initial test...');
                loadMockData();
            }, 1000);
        };
    </script>
</body>
</html>
