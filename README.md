# Chatwoot Helper React App

A React-based dashboard application for Chatwoot that integrates with the official Chatwoot Dashboard App API to provide real-time interaction with conversations, contacts, and agents.

## Features

- **Official Dashboard App Integration**: Uses Chatwoot's official Dashboard App API
- **Real-time Data**: Receives live conversation and contact data from Chatwoot
- **Agent Information**: Display current agent details
- **Conversation Management**: View active conversation details and messages
- **Contact Details**: Access comprehensive contact information
- **Event Logging**: Real-time event tracking and logging
- **Iframe Embedding**: Properly configured for embedding in Chatwoot
- **Theme Support**: Automatic light/dark theme detection
- **Responsive Design**: Mobile-friendly interface using Tailwind CSS

## Tech Stack

- **React 18** - Modern React with hooks
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **Chatwoot Dashboard App API** - Official integration with Chatwoot platform

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation

1. Install dependencies:

```bash
npm install
```

2. Start the development server:

```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:3000`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Usage

This application is designed to be embedded within a Chatwoot Dashboard App iframe. It automatically detects and connects to the Chatwoot SDK when loaded in the proper context.

### 🚨 Iframe Embedding Support

The app is configured to work properly when embedded in iframes by:

- Setting `X-Frame-Options: ALLOWALL` headers
- Configuring `Content-Security-Policy: frame-ancestors *;`
- Enabling CORS for cross-origin requests

This prevents the common "Refused to display in a frame" error when embedding in Chatwoot.

### Key Components

- **DetailRenderer**: Utility component for displaying object data
- **Event Logging**: Tracks all SDK interactions and events
- **Message Interface**: Send messages through the Chatwoot SDK
- **Theme Detection**: Automatically adapts to Chatwoot's theme

## Development

### Available Scripts

- `npm run dev` - Start development server (with iframe support)
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run start` - Start production server with Express
- `npm run serve` - Build and start production server
- `npm run lint` - Run ESLint

### Project Structure

```
src/
├── App.jsx          # Main application component
├── main.jsx         # React entry point
└── index.css        # Global styles and Tailwind imports
```

## Chatwoot Dashboard App Integration

The app follows the official Chatwoot Dashboard App specification:

### Message Handling

- Listens for `message` events from Chatwoot parent window
- Processes `appContext` events containing conversation, contact, and agent data
- Validates JSON data before processing

### Data Requests

- Uses `window.parent.postMessage('chatwoot-dashboard-app:fetch-info', '*')` to request data
- Automatically requests data on app initialization
- Provides manual refresh functionality

### Event Types

- `appContext` - Main event containing conversation, contact, and currentAgent data
- `message_received` - Logs all incoming messages for debugging
- `appContext_processed` - Confirms successful data processing

## Deployment

For detailed deployment instructions, see [DEPLOYMENT.md](./DEPLOYMENT.md).

### Quick Deploy Options:

1. **Node.js/Express** (Recommended):

   ```bash
   npm run serve
   ```

2. **Static Hosting** (Netlify, Vercel, etc.):

   ```bash
   npm run build
   # Deploy the dist/ folder
   ```

3. **Docker**:
   ```bash
   docker build -t chatwoot-helper .
   docker run -p 3000:3000 chatwoot-helper
   ```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
