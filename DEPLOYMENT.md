# Deployment Guide for Chatwoot Helper React App

This guide covers different deployment options for the Chatwoot Helper React app, with special consideration for iframe embedding requirements.

## 🚨 Important: Iframe Embedding Configuration

This app is designed to be embedded in Chatwoot as a Dashboard App. To prevent `X-Frame-Options` errors, the following configurations have been implemented:

### Development Server
The Vite development server is configured to allow iframe embedding:
```bash
npm run dev
```

### Production Build
```bash
npm run build
```

## Deployment Options

### 1. Node.js/Express Server (Recommended)

The project includes a custom Express server (`server.js`) that properly handles iframe embedding headers.

```bash
# Build and start production server
npm run serve

# Or separately:
npm run build
npm start
```

The server will run on port 3000 (or PORT environment variable) and includes:
- Proper iframe embedding headers
- CORS configuration
- Static file serving
- SPA routing support

### 2. Static File Hosting

For static hosting platforms, use the files in the `dist/` directory after running `npm run build`.

#### Netlify
1. Build the project: `npm run build`
2. Deploy the `dist/` folder
3. Copy `_headers` file to the root of your site
4. Configure redirects for SPA routing

#### Vercel
1. Build the project: `npm run build`
2. Deploy the `dist/` folder
3. Add `vercel.json` configuration (see below)

#### Apache Server
1. Build the project: `npm run build`
2. Copy contents of `dist/` to your web root
3. Ensure `public/.htaccess` is copied to the web root
4. Enable mod_headers and mod_rewrite in Apache

#### Nginx
Add the following to your Nginx configuration:
```nginx
location / {
    add_header X-Frame-Options "ALLOWALL";
    add_header Content-Security-Policy "frame-ancestors *;";
    add_header Access-Control-Allow-Origin "*";
    try_files $uri $uri/ /index.html;
}
```

### 3. Docker Deployment

Create a `Dockerfile`:
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

Build and run:
```bash
docker build -t chatwoot-helper .
docker run -p 3000:3000 chatwoot-helper
```

## Configuration Files

### `_headers` (Netlify)
```
/*
  X-Frame-Options: ALLOWALL
  Content-Security-Policy: frame-ancestors *;
```

### `vercel.json` (Vercel)
```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "ALLOWALL"
        },
        {
          "key": "Content-Security-Policy",
          "value": "frame-ancestors *;"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

## Environment Variables

Set the following environment variables for production:

- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Set to "production" for production builds

## Testing Iframe Embedding

To test iframe embedding locally:

1. Start the server: `npm run dev` or `npm start`
2. Create a test HTML file:
```html
<!DOCTYPE html>
<html>
<head>
    <title>Iframe Test</title>
</head>
<body>
    <h1>Testing Chatwoot Helper in Iframe</h1>
    <iframe 
        src="http://localhost:3000" 
        width="100%" 
        height="600px"
        frameborder="0">
    </iframe>
</body>
</html>
```

## Troubleshooting

### "Refused to display in a frame" Error
- Ensure X-Frame-Options is set to "ALLOWALL" or removed
- Check Content-Security-Policy allows frame-ancestors
- Verify your hosting platform supports custom headers

### CORS Issues
- Ensure Access-Control-Allow-Origin is set to "*" or specific domains
- Check that preflight OPTIONS requests are handled

### SPA Routing Issues
- Configure your server to serve index.html for all routes
- Use hash routing if server configuration is not possible

## Security Considerations

⚠️ **Important**: This configuration allows the app to be embedded in any iframe. For production use, consider:

1. Restricting frame-ancestors to specific Chatwoot domains
2. Implementing additional authentication
3. Using HTTPS in production
4. Validating the parent frame origin

Example of restricted CSP:
```
Content-Security-Policy: frame-ancestors https://your-chatwoot-domain.com;
```
