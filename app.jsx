import React, { useState, useEffect, useCallback } from 'react';

// Helper function to render object details nicely
const DetailRenderer = ({ title, data }) => {
  if (!data) {
    return (
      <div className="p-4 mb-4 bg-gray-700 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-blue-300 mb-2">{title}</h3>
        <p className="text-gray-400">No data available.</p>
      </div>
    );
  }

  const renderValue = (value) => {
    if (typeof value === 'object' && value !== null) {
      return <pre className="text-xs bg-gray-800 p-2 rounded overflow-x-auto">{JSON.stringify(value, null, 2)}</pre>;
    }
    return <span className="text-gray-300">{String(value)}</span>;
  };

  return (
    <div className="p-4 mb-4 bg-gray-700 rounded-lg shadow">
      <h3 className="text-lg font-semibold text-blue-300 mb-2">{title}</h3>
      {Object.entries(data).map(([key, value]) => (
        <div key={key} className="mb-1 text-sm">
          <strong className="text-blue-400">{key}: </strong>
          {renderValue(value)}
        </div>
      ))}
    </div>
  );
};


function App() {
  const [chatwootSDK, setChatwootSDK] = useState(null);
  const [isSDKReady, setIsSDKReady] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [currentConversation, setCurrentConversation] = useState(null);
  const [contact, setContact] = useState(null);
  const [theme, setTheme] = useState('light');
  const [newMessage, setNewMessage] = useState('');
  const [eventLog, setEventLog] = useState([]);
  const [error, setError] = useState(null);

  const addEventToLog = useCallback((eventData) => {
    console.log("Chatwoot Event Received:", eventData);
    setEventLog(prevLog => [
      { timestamp: new Date().toISOString(), ...eventData },
      ...prevLog.slice(0, 19) // Keep last 20 events
    ]);
  }, []);

  const fetchInitialData = useCallback(async (sdk) => {
    if (!sdk) return;
    try {
      setError(null);
      const user = await sdk.getUser();
      setCurrentUser(user);
      addEventToLog({ event_name: 'sdk.getUser', data: user });

      const conversation = await sdk.getConversation();
      setCurrentConversation(conversation);
      addEventToLog({ event_name: 'sdk.getConversation', data: conversation });
      
      // If conversation exists, try to get contact
      if (conversation && conversation.meta && conversation.meta.sender) {
        // Assuming getContact might need an ID or relies on current context
        // The Chatwoot SDK docs for dashboard apps are a bit sparse on getContact parameters.
        // Often, getContact() might implicitly use the current conversation's contact.
        try {
            const contactData = await sdk.getContact(); // Or sdk.getContact(conversation.meta.sender.id) if API allows
            setContact(contactData);
            addEventToLog({ event_name: 'sdk.getContact', data: contactData });
        } catch (contactError) {
            console.error("Error fetching contact:", contactError);
            setError("Error fetching contact. See console for details.");
            addEventToLog({ event_name: 'sdk.getContact.error', data: contactError.message });
        }
      } else if (conversation) {
         addEventToLog({ event_name: 'sdk.getContact.skipped', data: 'No sender info in conversation to fetch contact.' });
      }


      const appTheme = await sdk.getAppTheme();
      setTheme(appTheme || 'light'); // Default to light if undefined
      addEventToLog({ event_name: 'sdk.getAppTheme', data: appTheme });

    } catch (e) {
      console.error("Error fetching initial data from Chatwoot SDK:", e);
      setError(`Error fetching initial data: ${e.message}. Check console.`);
      addEventToLog({ event_name: 'sdk.fetchInitialData.error', data: e.message });
    }
  }, [addEventToLog]);

  // Effect for Chatwoot SDK initialization
  useEffect(() => {
    const handleSDKReady = () => {
      if (window.chatwootSDK) {
        console.log("Chatwoot SDK is ready.");
        setChatwootSDK(() => window.chatwootSDK); // Use functional update for safety
        setIsSDKReady(true);
        addEventToLog({ event_name: 'chatwoot:ready', data: 'SDK initialized' });
        fetchInitialData(window.chatwootSDK);
      } else {
        console.error("chatwoot:ready event fired, but window.chatwootSDK is not available.");
        setError("Chatwoot SDK not found after ready event.");
        addEventToLog({ event_name: 'chatwoot:ready.error', data: 'window.chatwootSDK not found' });
      }
    };

    window.addEventListener('chatwoot:ready', handleSDKReady);
    
    // Attempt to initialize if SDK is already available (e.g. on hot reload)
    if (window.chatwootSDK && !isSDKReady) {
        console.log("Chatwoot SDK already available on mount.");
        handleSDKReady();
    }


    return () => {
      window.removeEventListener('chatwoot:ready', handleSDKReady);
    };
  }, [fetchInitialData, addEventToLog, isSDKReady]); // Added isSDKReady to dependencies

  // Effect for Chatwoot events
  useEffect(() => {
    if (!isSDKReady || !chatwootSDK) return;

    const handleChatwootEvent = (event) => {
      const { detail } = event;
      addEventToLog(detail); // detail should contain event_name and data

      if (detail && detail.event_name) {
        switch (detail.event_name) {
          case 'conversation.updated':
            setCurrentConversation(prev => ({ ...prev, ...detail.data }));
            break;
          case 'contact.updated':
            setContact(prev => ({ ...prev, ...detail.data }));
            break;
          case 'message.created':
            // May need to refresh conversation or messages if displaying them
            // For now, just log it. A full message list display would be more complex.
            console.log('New message created:', detail.data);
            // Potentially re-fetch conversation to get latest messages array
            // chatwootSDK.getConversation().then(setCurrentConversation);
            break;
          case 'app.theme_changed': // Hypothetical event name for theme change
             if (detail.data && detail.data.theme) {
                setTheme(detail.data.theme);
             }
             break;
          default:
            // console.log('Unhandled Chatwoot event:', detail.event_name);
            break;
        }
      }
    };

    window.addEventListener('chatwoot:event', handleChatwootEvent);

    // Example of specific SDK event listeners (might be for client SDK, but good to know)
    // chatwootSDK.on('message', (data) => addEventToLog({ event_name: 'sdk.on.message', data }));
    // chatwootSDK.on('conversation.updated', (data) => {
    //   addEventToLog({ event_name: 'sdk.on.conversation.updated', data });
    //   setCurrentConversation(prev => ({...prev, ...data}));
    // });

    return () => {
      window.removeEventListener('chatwoot:event', handleChatwootEvent);
      // If specific listeners were added:
      // chatwootSDK.off('message');
      // chatwootSDK.off('conversation.updated');
    };
  }, [isSDKReady, chatwootSDK, addEventToLog]);

  const handleSendMessage = async () => {
    if (!chatwootSDK || !newMessage.trim()) return;
    try {
      setError(null);
      await chatwootSDK.sendMessage({ content: newMessage });
      addEventToLog({ event_name: 'sdk.sendMessage.success', data: { content: newMessage } });
      setNewMessage('');
    } catch (e) {
      console.error("Error sending message:", e);
      setError(`Error sending message: ${e.message}. Check console.`);
      addEventToLog({ event_name: 'sdk.sendMessage.error', data: e.message });
    }
  };
  
  // Dynamic body class for theme
  useEffect(() => {
    document.body.className = theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-100 text-black';
  }, [theme]);

  if (!isSDKReady && !window.chatwootSDK) { // Check window.chatwootSDK for initial load before ready event
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-800 text-white p-4">
        <div className="text-center">
          <svg className="animate-spin h-10 w-10 text-blue-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <h1 className="text-2xl font-semibold">Waiting for Chatwoot SDK...</h1>
          <p className="text-gray-400 mt-2">This app needs to be loaded within Chatwoot.</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`p-4 md:p-6 min-h-screen font-inter ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'}`}>
      <header className="mb-6">
        <h1 className={`text-3xl font-bold ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}>Chatwoot Dashboard App</h1>
        <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>Interacting with Chatwoot SDK (Theme: {theme})</p>
      </header>

      {error && (
        <div className="mb-4 p-3 bg-red-500 text-white rounded-md shadow">
          <strong>Error:</strong> {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        <div className={`p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Agent Info</h2>
          {currentUser ? <DetailRenderer title="Current User" data={currentUser} /> : <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>Loading agent info...</p>}
        </div>

        <div className={`p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Conversation Details</h2>
          {currentConversation ? <DetailRenderer title="Current Conversation" data={currentConversation} /> : <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No active conversation or loading...</p>}
        </div>
        
        <div className={`p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Contact Details</h2>
          {contact ? <DetailRenderer title="Contact" data={contact} /> : <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No contact loaded or loading...</p>}
        </div>
      </div>

      <div className={`mt-6 p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
        <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Send Message</h2>
        <textarea
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Type your message..."
          rows="3"
          className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 ${theme === 'dark' ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-black placeholder-gray-500'}`}
          disabled={!isSDKReady}
        />
        <button
          onClick={handleSendMessage}
          disabled={!isSDKReady || !newMessage.trim()}
          className={`mt-2 px-4 py-2 rounded-md font-semibold transition-colors
            ${isSDKReady && newMessage.trim()
              ? (theme === 'dark' ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-blue-500 hover:bg-blue-600 text-white')
              : (theme === 'dark' ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-gray-300 text-gray-500 cursor-not-allowed')}`}
        >
          Send Message
        </button>
      </div>

      <div className={`mt-6 p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
        <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Event Log</h2>
        {eventLog.length === 0 ? (
          <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No events captured yet.</p>
        ) : (
          <ul className="space-y-2 max-h-96 overflow-y-auto text-xs">
            {eventLog.map((log, index) => (
              <li key={index} className={`p-2 rounded ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <p className="font-mono">
                  <span className="font-semibold text-purple-400">{log.timestamp}: </span>
                  <span className="font-semibold text-green-400">{log.event_name}</span>
                </p>
                <pre className={`mt-1 p-1.5 rounded text-xs overflow-x-auto ${theme === 'dark' ? 'bg-gray-900 text-gray-300' : 'bg-gray-200 text-gray-700'}`}>
                  {JSON.stringify(log.data, null, 2)}
                </pre>
              </li>
            ))}
          </ul>
        )}
      </div>
       <footer className={`mt-8 text-center text-sm ${theme === 'dark' ? 'text-gray-500' : 'text-gray-500'}`}>
        <p>Chatwoot Dashboard App Demo</p>
        <p>Ensure this app is embedded within a Chatwoot Dashboard App iframe.</p>
      </footer>
    </div>
  );
}

export default App;
