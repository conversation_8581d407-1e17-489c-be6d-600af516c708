<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatwoot Dashboard App Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .simulator {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .chatwoot-frame {
            background: #1f2937;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        .primary { background: #3b82f6; color: white; }
        .primary:hover { background: #2563eb; }
        .secondary { background: #6b7280; color: white; }
        .secondary:hover { background: #4b5563; }
        .success { background: #10b981; color: white; }
        .success:hover { background: #059669; }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        .timestamp {
            color: #666;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Chatwoot Dashboard App Simulator</h1>
        
        <div class="info">
            <h3>📋 About This Simulator</h3>
            <p>This page simulates the Chatwoot Dashboard App environment by sending the official appContext events to the embedded iframe.</p>
            <p><strong>Purpose:</strong> Test the Chatwoot Helper app as if it were running inside Chatwoot.</p>
        </div>

        <div class="simulator">
            <h2>🎮 Dashboard App Controls</h2>
            <div class="controls">
                <button class="primary" onclick="sendAppContext()">Send App Context</button>
                <button class="secondary" onclick="sendConversationUpdate()">Update Conversation</button>
                <button class="secondary" onclick="sendContactUpdate()">Update Contact</button>
                <button class="success" onclick="sendFullContext()">Send Full Context</button>
                <button class="secondary" onclick="clearLog()">Clear Log</button>
            </div>
            
            <div class="log" id="eventLog">
                <div class="log-entry">
                    <span class="timestamp">[Ready]</span> Simulator ready. Click buttons above to send events to the Dashboard App.
                </div>
            </div>
        </div>

        <div class="chatwoot-frame">
            <h2 style="color: white; margin-top: 0;">📱 Embedded Dashboard App</h2>
            <iframe 
                id="dashboardApp"
                src="http://localhost:3000" 
                title="Chatwoot Helper Dashboard App">
                <p>Your browser does not support iframes.</p>
            </iframe>
        </div>
    </div>

    <script>
        const iframe = document.getElementById('dashboardApp');
        const log = document.getElementById('eventLog');

        function addLogEntry(message) {
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function sendMessageToApp(data) {
            try {
                iframe.contentWindow.postMessage(JSON.stringify(data), '*');
                addLogEntry(`✅ Sent: ${data.event} event`);
            } catch (error) {
                addLogEntry(`❌ Error: ${error.message}`);
            }
        }

        function sendAppContext() {
            const appContext = {
                event: "appContext",
                data: {
                    conversation: {
                        id: 12345,
                        status: "open",
                        meta: {
                            sender: {
                                id: 67890,
                                name: "John Doe",
                                email: "<EMAIL>",
                                phone_number: "+1234567890",
                                additional_attributes: {
                                    company_name: "Acme Corp",
                                    description: "Premium customer"
                                }
                            },
                            assignee: {
                                id: 1,
                                name: "Agent Smith",
                                email: "<EMAIL>"
                            }
                        },
                        messages: [
                            {
                                id: 1,
                                content: "Hello, I need help with my order",
                                message_type: 0,
                                created_at: Date.now() - 300000
                            }
                        ]
                    },
                    contact: {
                        id: 67890,
                        name: "John Doe",
                        email: "<EMAIL>",
                        phone_number: "+1234567890",
                        additional_attributes: {
                            company_name: "Acme Corp",
                            description: "Premium customer",
                            social_profiles: {
                                linkedin: "johndoe",
                                twitter: "@johndoe"
                            }
                        },
                        custom_attributes: {
                            subscription_plan: "premium",
                            last_purchase: "2024-01-15"
                        }
                    },
                    currentAgent: {
                        id: 1,
                        name: "Agent Smith",
                        email: "<EMAIL>"
                    }
                }
            };
            sendMessageToApp(appContext);
        }

        function sendConversationUpdate() {
            const update = {
                event: "appContext",
                data: {
                    conversation: {
                        id: 12345,
                        status: "resolved",
                        updated_at: Date.now()
                    }
                }
            };
            sendMessageToApp(update);
        }

        function sendContactUpdate() {
            const update = {
                event: "appContext", 
                data: {
                    contact: {
                        id: 67890,
                        name: "John Doe Updated",
                        email: "<EMAIL>",
                        additional_attributes: {
                            company_name: "Acme Corp Updated",
                            description: "VIP customer"
                        }
                    }
                }
            };
            sendMessageToApp(update);
        }

        function sendFullContext() {
            // Send a comprehensive context with all data
            sendAppContext();
            setTimeout(() => {
                addLogEntry("📊 Full context sent with conversation, contact, and agent data");
            }, 100);
        }

        function clearLog() {
            log.innerHTML = '<div class="log-entry"><span class="timestamp">[Cleared]</span> Log cleared.</div>';
        }

        // Listen for messages from the iframe
        window.addEventListener('message', function(event) {
            if (event.data === 'chatwoot-dashboard-app:fetch-info') {
                addLogEntry("📥 Received fetch-info request from Dashboard App");
                // Automatically send context when requested
                setTimeout(sendAppContext, 100);
            }
        });

        // Auto-send context when iframe loads
        iframe.onload = function() {
            addLogEntry("🔄 Dashboard App iframe loaded");
            setTimeout(() => {
                addLogEntry("🚀 Auto-sending initial app context...");
                sendAppContext();
            }, 1000);
        };
    </script>
</body>
</html>
