<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatwoot Dashboard App Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .simulator {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .chatwoot-frame {
            background: #1f2937;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        .primary { background: #3b82f6; color: white; }
        .primary:hover { background: #2563eb; }
        .secondary { background: #6b7280; color: white; }
        .secondary:hover { background: #4b5563; }
        .success { background: #10b981; color: white; }
        .success:hover { background: #059669; }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        .timestamp {
            color: #666;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Chatwoot Dashboard App Simulator</h1>

        <div class="info">
            <h3>📋 About This Simulator</h3>
            <p>This page simulates the Chatwoot Dashboard App environment by sending the official appContext events to the embedded iframe.</p>
            <p><strong>Purpose:</strong> Test the Chatwoot Helper app as if it were running inside Chatwoot.</p>
        </div>

        <div class="simulator">
            <h2>🎮 Dashboard App Controls</h2>
            <div class="controls">
                <button class="primary" onclick="sendAppContext()">Send App Context</button>
                <button class="secondary" onclick="sendConversationUpdate()">Update Conversation</button>
                <button class="secondary" onclick="sendContactUpdate()">Update Contact</button>
                <button class="success" onclick="sendFullContext()">Send Full Context</button>
                <button class="secondary" onclick="clearLog()">Clear Log</button>
            </div>

            <div class="log" id="eventLog">
                <div class="log-entry">
                    <span class="timestamp">[Ready]</span> Simulator ready. Click buttons above to send events to the Dashboard App.
                </div>
            </div>
        </div>

        <div class="chatwoot-frame">
            <h2 style="color: white; margin-top: 0;">📱 Embedded Dashboard App</h2>
            <iframe
                id="dashboardApp"
                src="http://localhost:3000"
                title="Chatwoot Helper Dashboard App">
                <p>Your browser does not support iframes.</p>
            </iframe>
        </div>
    </div>

    <script>
        const iframe = document.getElementById('dashboardApp');
        const log = document.getElementById('eventLog');

        function addLogEntry(message) {
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function sendMessageToApp(data) {
            try {
                iframe.contentWindow.postMessage(JSON.stringify(data), '*');
                addLogEntry(`✅ Sent: ${data.event} event`);
            } catch (error) {
                addLogEntry(`❌ Error: ${error.message}`);
            }
        }

        function sendAppContext() {
            // Real mock data from Chatwoot
            const appContext = {
                event: "appContext",
                data: {
                    conversation: {
                        meta: {
                            sender: {
                                additional_attributes: {
                                    city: "",
                                    country: "",
                                    description: "",
                                    company_name: "",
                                    country_code: "",
                                    social_profiles: {
                                        instagram: "ahmed_magdy412"
                                    }
                                },
                                availability_status: "offline",
                                email: null,
                                id: 2,
                                name: "Ahmed Magdy",
                                phone_number: "+201128829358",
                                blocked: false,
                                identifier: "<EMAIL>",
                                thumbnail: "https://Chatwoot.lowcalories.ae/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBRdz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--b43adae1dfc2f43b40788dd9052359a363d2885b/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--1578d1a194762faed72c061c5d274341af8acd62/461244390_890953046575651_617710305352355255_n.jpg",
                                custom_attributes: {},
                                last_activity_at: **********,
                                created_at: **********
                            },
                            channel: "Channel::Api",
                            hmac_verified: false
                        },
                        id: 9,
                        messages: [
                            {
                                id: 603,
                                content: "who?",
                                inbox_id: 1,
                                conversation_id: 9,
                                message_type: 0,
                                content_type: "text",
                                status: "sent",
                                content_attributes: {},
                                created_at: 1748778926,
                                private: false,
                                source_id: "WAID:3F86EF53B6AC736C7031"
                            },
                            {
                                id: 621,
                                content: "Nice to meet you 😊",
                                inbox_id: 1,
                                conversation_id: 9,
                                message_type: 0,
                                content_type: "text",
                                status: "sent",
                                content_attributes: {},
                                created_at: 1748779551,
                                private: false,
                                source_id: "WAID:F28ABB47725666D3029286C0467CDE35"
                            },
                            {
                                id: 628,
                                content: "السلام عليكم",
                                inbox_id: 1,
                                conversation_id: 9,
                                message_type: 0,
                                content_type: "text",
                                status: "sent",
                                content_attributes: {},
                                created_at: **********,
                                private: false,
                                source_id: "WAID:5EA1FDAA5D07C45017E023FCA5949DA3"
                            }
                        ],
                        account_id: 1,
                        uuid: "0b173d07-aeb1-4d62-b1e2-533d4a267df9",
                        additional_attributes: {},
                        agent_last_seen_at: **********,
                        assignee_last_seen_at: 0,
                        can_reply: true,
                        contact_last_seen_at: **********,
                        custom_attributes: {},
                        inbox_id: 1,
                        labels: [],
                        muted: false,
                        snoozed_until: null,
                        status: "open",
                        created_at: **********,
                        updated_at: **********.319895,
                        timestamp: **********,
                        first_reply_created_at: **********,
                        unread_count: 0
                    },
                    contact: {
                        additional_attributes: {
                            city: "",
                            country: "",
                            description: "",
                            company_name: "",
                            country_code: "",
                            social_profiles: {
                                instagram: "ahmed_magdy412"
                            }
                        },
                        availability_status: "offline",
                        email: null,
                        id: 2,
                        name: "Ahmed Magdy",
                        phone_number: "+201128829358",
                        blocked: false,
                        identifier: "<EMAIL>",
                        thumbnail: "https://Chatwoot.lowcalories.ae/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBRdz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--b43adae1dfc2f43b40788dd9052359a363d2885b/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--1578d1a194762faed72c061c5d274341af8acd62/461244390_890953046575651_617710305352355255_n.jpg",
                        custom_attributes: {},
                        last_activity_at: **********,
                        created_at: **********,
                        contact_inboxes: [
                            {
                                inbox: {
                                    id: 1,
                                    avatar_url: "",
                                    channel_id: 1,
                                    name: "Demo Account",
                                    channel_type: "Channel::Api",
                                    provider: null
                                },
                                source_id: "fd7b01de-a193-4770-bced-652255f4e582"
                            }
                        ]
                    },
                    currentAgent: {
                        id: 1,
                        name: "Ahmed Magdy",
                        email: "<EMAIL>"
                    }
                }
            };
            sendMessageToApp(appContext);
        }

        function sendConversationUpdate() {
            const update = {
                event: "appContext",
                data: {
                    conversation: {
                        id: 12345,
                        status: "resolved",
                        updated_at: Date.now()
                    }
                }
            };
            sendMessageToApp(update);
        }

        function sendContactUpdate() {
            const update = {
                event: "appContext",
                data: {
                    contact: {
                        id: 67890,
                        name: "John Doe Updated",
                        email: "<EMAIL>",
                        additional_attributes: {
                            company_name: "Acme Corp Updated",
                            description: "VIP customer"
                        }
                    }
                }
            };
            sendMessageToApp(update);
        }

        function sendFullContext() {
            // Send a comprehensive context with all data
            sendAppContext();
            setTimeout(() => {
                addLogEntry("📊 Full context sent with conversation, contact, and agent data");
            }, 100);
        }

        function clearLog() {
            log.innerHTML = '<div class="log-entry"><span class="timestamp">[Cleared]</span> Log cleared.</div>';
        }

        // Listen for messages from the iframe
        window.addEventListener('message', function(event) {
            if (event.data === 'chatwoot-dashboard-app:fetch-info') {
                addLogEntry("📥 Received fetch-info request from Dashboard App");
                // Automatically send context when requested
                setTimeout(sendAppContext, 100);
            }
        });

        // Auto-send context when iframe loads
        iframe.onload = function() {
            addLogEntry("🔄 Dashboard App iframe loaded");
            setTimeout(() => {
                addLogEntry("🚀 Auto-sending initial app context...");
                sendAppContext();
            }, 1000);
        };
    </script>
</body>
</html>
