<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Check - Chatwoot Helper</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-box {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Server & Component Check</h1>
        
        <div class="error status-box">
            <strong>🚨 CRITICAL TEST:</strong> If you don't see a YELLOW BOX with red border in the app below, the Messages component is not rendering!
        </div>

        <div class="info status-box">
            <strong>Expected Result:</strong> Large yellow box with "🚨 MESSAGES COMPONENT IS WORKING! 🚨" text
        </div>

        <h2>🎮 Test Controls</h2>
        <button onclick="checkServer()">🌐 Check Server Status</button>
        <button onclick="checkHTML()">📄 Check HTML Content</button>
        <button onclick="checkJS()">⚙️ Check JavaScript</button>
        <button onclick="forceRefresh()">🔄 Force Refresh</button>

        <div id="testResults" class="test-result" style="display: none;">
            <strong>Test Results:</strong><br>
            <div id="resultContent"></div>
        </div>

        <h2>📱 Live App Test</h2>
        <div class="warning status-box">
            <strong>LOOK FOR:</strong> Yellow box with red border and "MESSAGES COMPONENT IS WORKING!" text
        </div>

        <iframe 
            id="appFrame"
            src="http://localhost:3000?force-refresh=true" 
            title="Chatwoot Helper App">
        </iframe>

        <h2>🔧 Manual Troubleshooting</h2>
        <div class="info status-box">
            <strong>If yellow box is NOT visible:</strong><br>
            1. Press F12 to open DevTools<br>
            2. Go to Console tab<br>
            3. Look for errors (red text)<br>
            4. Check Network tab for failed requests<br>
            5. Try hard refresh: Ctrl+Shift+R
        </div>
    </div>

    <script>
        let testResults = document.getElementById('testResults');
        let resultContent = document.getElementById('resultContent');

        function showResults(message) {
            testResults.style.display = 'block';
            resultContent.innerHTML += `[${new Date().toLocaleTimeString()}] ${message}<br>`;
            testResults.scrollTop = testResults.scrollHeight;
        }

        function checkServer() {
            showResults('🌐 Checking server status...');
            
            fetch('http://localhost:3000')
                .then(response => {
                    showResults(`✅ Server responding: ${response.status} ${response.statusText}`);
                    showResults(`📊 Content-Type: ${response.headers.get('content-type')}`);
                    return response.text();
                })
                .then(html => {
                    showResults(`📄 HTML length: ${html.length} characters`);
                    if (html.includes('MESSAGES COMPONENT IS WORKING')) {
                        showResults('✅ Messages component text found in HTML!');
                    } else {
                        showResults('❌ Messages component text NOT found in HTML');
                    }
                    if (html.includes('root')) {
                        showResults('✅ React root element found');
                    } else {
                        showResults('❌ React root element NOT found');
                    }
                })
                .catch(error => {
                    showResults(`❌ Server error: ${error.message}`);
                });
        }

        function checkHTML() {
            showResults('📄 Checking HTML content...');
            
            fetch('http://localhost:3000')
                .then(response => response.text())
                .then(html => {
                    showResults('📋 HTML Content Analysis:');
                    showResults(`- Contains "react": ${html.includes('react') ? '✅' : '❌'}`);
                    showResults(`- Contains "Messages": ${html.includes('Messages') ? '✅' : '❌'}`);
                    showResults(`- Contains script tags: ${html.includes('<script') ? '✅' : '❌'}`);
                    showResults(`- Contains CSS links: ${html.includes('<link') ? '✅' : '❌'}`);
                    
                    // Show first 500 characters
                    showResults('📝 HTML Preview:');
                    showResults(html.substring(0, 500) + '...');
                })
                .catch(error => {
                    showResults(`❌ HTML check error: ${error.message}`);
                });
        }

        function checkJS() {
            showResults('⚙️ Checking JavaScript...');
            showResults('🔍 Open browser console (F12) and look for:');
            showResults('   🚀 App.jsx loaded, Messages component imported:');
            showResults('   🎯 About to render Messages component');
            showResults('   🎨 MESSAGES COMPONENT IS RENDERING!!!');
            showResults('   ❌ Any red error messages');
        }

        function forceRefresh() {
            showResults('🔄 Force refreshing app...');
            const iframe = document.getElementById('appFrame');
            const timestamp = Date.now();
            iframe.src = `http://localhost:3000?force-refresh=${timestamp}`;
            
            setTimeout(() => {
                showResults('✅ App refreshed with timestamp: ' + timestamp);
                showResults('👀 Check if yellow box is now visible');
            }, 2000);
        }

        // Auto-check when page loads
        window.onload = function() {
            setTimeout(checkServer, 1000);
        };
    </script>
</body>
</html>
