import React, { useState, useEffect, useCallback } from 'react';

// Helper function to render object details nicely
const DetailRenderer = ({ title, data }) => {
  if (!data) {
    return (
      <div className="p-4 mb-4 bg-gray-700 rounded-lg shadow">
        <h3 className="text-lg font-semibold text-blue-300 mb-2">{title}</h3>
        <p className="text-gray-400">No data available.</p>
      </div>
    );
  }

  const renderValue = (value) => {
    if (typeof value === 'object' && value !== null) {
      return <pre className="text-xs bg-gray-800 p-2 rounded overflow-x-auto">{JSON.stringify(value, null, 2)}</pre>;
    }
    return <span className="text-gray-300">{String(value)}</span>;
  };

  return (
    <div className="p-4 mb-4 bg-gray-700 rounded-lg shadow">
      <h3 className="text-lg font-semibold text-blue-300 mb-2">{title}</h3>
      {Object.entries(data).map(([key, value]) => (
        <div key={key} className="mb-1 text-sm">
          <strong className="text-blue-400">{key}: </strong>
          {renderValue(value)}
        </div>
      ))}
    </div>
  );
};


function App() {
  const [isAppReady, setIsAppReady] = useState(false);
  const [currentAgent, setCurrentAgent] = useState(null);
  const [currentConversation, setCurrentConversation] = useState(null);
  const [contact, setContact] = useState(null);
  const [theme, setTheme] = useState('light');
  const [eventLog, setEventLog] = useState([]);
  const [error, setError] = useState(null);
  const [appContext, setAppContext] = useState(null);

  const addEventToLog = useCallback((eventData) => {
    console.log("Chatwoot Event Received:", eventData);
    setEventLog(prevLog => [
      { timestamp: new Date().toISOString(), ...eventData },
      ...prevLog.slice(0, 19) // Keep last 20 events
    ]);
  }, []);

  // Helper function to validate JSON
  const isJSONValid = useCallback((str) => {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }, []);

  // Request data from Chatwoot on demand
  const requestChatwootData = useCallback(() => {
    try {
      window.parent.postMessage('chatwoot-dashboard-app:fetch-info', '*');
      addEventToLog({ event_name: 'chatwoot-dashboard-app:fetch-info', data: 'Requested data from Chatwoot' });
    } catch (e) {
      console.error("Error requesting data from Chatwoot:", e);
      addEventToLog({ event_name: 'chatwoot-dashboard-app:fetch-info.error', data: e.message });
    }
  }, [addEventToLog]);

  // Effect for Chatwoot Dashboard App message handling
  useEffect(() => {
    const handleChatwootMessage = (event) => {
      // Validate JSON data
      if (!isJSONValid(event.data)) {
        return;
      }

      const eventData = JSON.parse(event.data);
      addEventToLog({ event_name: 'message_received', data: eventData });

      // Handle appContext event from Chatwoot
      if (eventData.event === 'appContext') {
        console.log("Received appContext from Chatwoot:", eventData.data);
        setAppContext(eventData.data);
        setIsAppReady(true);

        // Extract data from the context
        if (eventData.data.conversation) {
          setCurrentConversation(eventData.data.conversation);
        }

        if (eventData.data.contact) {
          setContact(eventData.data.contact);
        }

        if (eventData.data.currentAgent) {
          setCurrentAgent(eventData.data.currentAgent);
        }

        addEventToLog({
          event_name: 'appContext_processed',
          data: {
            hasConversation: !!eventData.data.conversation,
            hasContact: !!eventData.data.contact,
            hasCurrentAgent: !!eventData.data.currentAgent
          }
        });
      }
    };

    // Listen for messages from Chatwoot
    window.addEventListener("message", handleChatwootMessage);

    // Request initial data from Chatwoot
    requestChatwootData();

    return () => {
      window.removeEventListener("message", handleChatwootMessage);
    };
  }, [addEventToLog, isJSONValid, requestChatwootData, setAppContext, setIsAppReady, setCurrentAgent]);

  // Handle refresh data button
  const handleRefreshData = useCallback(() => {
    requestChatwootData();
  }, [requestChatwootData]);

  // Dynamic body class for theme
  useEffect(() => {
    document.body.className = theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-100 text-black';
  }, [theme]);

  if (!isAppReady) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-800 text-white p-4">
        <div className="text-center">
          <svg className="animate-spin h-10 w-10 text-blue-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <h1 className="text-2xl font-semibold">Waiting for Chatwoot Dashboard App...</h1>
          <p className="text-gray-400 mt-2">This app needs to be loaded within Chatwoot as a Dashboard App.</p>
          <button
            onClick={handleRefreshData}
            className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
          >
            Request Data from Chatwoot
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 md:p-6 min-h-screen font-inter ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'}`}>
      <header className="mb-6">
        <h1 className={`text-3xl font-bold ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}>Chatwoot Dashboard App</h1>
        <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>Interacting with Chatwoot SDK (Theme: {theme})</p>
      </header>

      {error && (
        <div className="mb-4 p-3 bg-red-500 text-white rounded-md shadow">
          <strong>Error:</strong> {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        <div className={`p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Current Agent</h2>
          {currentAgent ? <DetailRenderer title="Current Agent" data={currentAgent} /> : <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>Loading agent info...</p>}
        </div>

        <div className={`p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Conversation Details</h2>
          {currentConversation ? <DetailRenderer title="Current Conversation" data={currentConversation} /> : <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No active conversation or loading...</p>}
        </div>

        <div className={`p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Contact Details</h2>
          {contact ? <DetailRenderer title="Contact" data={contact} /> : <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No contact loaded or loading...</p>}
        </div>
      </div>

      <div className={`mt-6 p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
        <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Dashboard App Controls</h2>
        <div className="flex gap-4 flex-wrap">
          <button
            onClick={handleRefreshData}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-semibold transition-colors"
          >
            Refresh Data from Chatwoot
          </button>
          <button
            onClick={() => setError(null)}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md font-semibold transition-colors"
          >
            Clear Errors
          </button>
        </div>
        <div className="mt-4 text-sm text-gray-500">
          <p><strong>App Status:</strong> {isAppReady ? '✅ Connected to Chatwoot' : '⏳ Waiting for Chatwoot'}</p>
          <p><strong>Data Received:</strong> {appContext ? '✅ Yes' : '❌ No'}</p>
          <p><strong>Events Logged:</strong> {eventLog.length}</p>
        </div>
      </div>

      <div className={`mt-6 p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
        <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Event Log</h2>
        {eventLog.length === 0 ? (
          <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No events captured yet.</p>
        ) : (
          <ul className="space-y-2 max-h-96 overflow-y-auto text-xs">
            {eventLog.map((log, index) => (
              <li key={index} className={`p-2 rounded ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <p className="font-mono">
                  <span className="font-semibold text-purple-400">{log.timestamp}: </span>
                  <span className="font-semibold text-green-400">{log.event_name}</span>
                </p>
                <pre className={`mt-1 p-1.5 rounded text-xs overflow-x-auto ${theme === 'dark' ? 'bg-gray-900 text-gray-300' : 'bg-gray-200 text-gray-700'}`}>
                  {JSON.stringify(log.data, null, 2)}
                </pre>
              </li>
            ))}
          </ul>
        )}
      </div>
       <footer className={`mt-8 text-center text-sm ${theme === 'dark' ? 'text-gray-500' : 'text-gray-500'}`}>
        <p>Chatwoot Helper - Dashboard App</p>
        <p>Integrated with Chatwoot using official Dashboard App API</p>
        <p>Listening for appContext events from Chatwoot parent window</p>
      </footer>
    </div>
  );
}

export default App;
