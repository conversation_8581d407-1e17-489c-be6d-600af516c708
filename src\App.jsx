import React, { useState, useEffect, useCallback } from 'react';
import Messages from './Messages';

// Helper function to render object details nicely
const DetailRenderer = ({ title, data, theme = 'dark' }) => {
  if (!data) {
    return (
      <div className={`p-4 mb-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
        <h3 className={`text-lg font-semibold mb-2 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>{title}</h3>
        <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No data available.</p>
      </div>
    );
  }

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'N/A';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString();
  };

  const renderValue = (key, value) => {
    // Special handling for specific fields
    if (key === 'created_at' || key === 'updated_at' || key === 'last_activity_at') {
      return <span className={`${theme === 'dark' ? 'text-green-300' : 'text-green-600'}`}>{formatTimestamp(value)}</span>;
    }

    if (key === 'phone_number') {
      return <span className={`${theme === 'dark' ? 'text-blue-300' : 'text-blue-600'}`}>{value}</span>;
    }

    if (key === 'email') {
      return <span className={`${theme === 'dark' ? 'text-purple-300' : 'text-purple-600'}`}>{value || 'Not provided'}</span>;
    }

    if (key === 'status') {
      const statusColor = value === 'open' ? 'text-green-400' : value === 'resolved' ? 'text-blue-400' : 'text-yellow-400';
      return <span className={statusColor}>● {value}</span>;
    }

    if (key === 'availability_status') {
      const statusColor = value === 'online' ? 'text-green-400' : 'text-gray-400';
      return <span className={statusColor}>● {value}</span>;
    }

    if (key === 'messages' && Array.isArray(value)) {
      return (
        <div className="mt-2">
          <span className={`${theme === 'dark' ? 'text-yellow-300' : 'text-yellow-600'}`}>
            {value.length} messages
          </span>
          <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
            {value.slice(-3).map((msg, idx) => (
              <div key={msg.id || idx} className={`p-2 rounded text-xs ${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200'}`}>
                <div className={`font-semibold ${theme === 'dark' ? 'text-blue-300' : 'text-blue-600'}`}>
                  {msg.message_type === 0 ? '👤 Customer' : '🤖 Agent'} - {formatTimestamp(msg.created_at)}
                </div>
                <div className={`mt-1 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                  {msg.content?.substring(0, 100)}{msg.content?.length > 100 ? '...' : ''}
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (key === 'thumbnail' && value) {
      return (
        <div className="flex items-center gap-2">
          <img src={value} alt="Thumbnail" className="w-8 h-8 rounded-full" onError={(e) => e.target.style.display = 'none'} />
          <span className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>Profile image</span>
        </div>
      );
    }

    if (typeof value === 'object' && value !== null) {
      // Don't show complex nested objects in detail view
      if (key === 'additional_attributes' || key === 'custom_attributes' || key === 'social_profiles') {
        const entries = Object.entries(value);
        if (entries.length === 0) return <span className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>None</span>;
        return (
          <div className="mt-1">
            {entries.map(([subKey, subValue]) => (
              <div key={subKey} className={`text-xs ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                <strong>{subKey}:</strong> {String(subValue)}
              </div>
            ))}
          </div>
        );
      }
      return <pre className={`text-xs p-2 rounded overflow-x-auto ${theme === 'dark' ? 'bg-gray-800 text-gray-300' : 'bg-gray-200 text-gray-700'}`}>{JSON.stringify(value, null, 2)}</pre>;
    }

    return <span className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>{String(value)}</span>;
  };

  // Filter out complex nested objects for cleaner display
  const filteredData = Object.entries(data).filter(([key]) =>
    !['contact_inboxes', 'sender', 'conversation', 'messages'].includes(key)
  );

  return (
    <div className={`p-4 mb-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
      <h3 className={`text-lg font-semibold mb-2 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>{title}</h3>
      <div className="space-y-2">
        {filteredData.map(([key, value]) => (
          <div key={key} className="text-sm">
            <strong className={`${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}>{key.replace(/_/g, ' ')}: </strong>
            {renderValue(key, value)}
          </div>
        ))}
      </div>
    </div>
  );
};


function App() {
  const [isAppReady, setIsAppReady] = useState(false);
  const [currentAgent, setCurrentAgent] = useState(null);
  const [currentConversation, setCurrentConversation] = useState(null);
  const [contact, setContact] = useState(null);
  const [theme, setTheme] = useState('light');
  const [eventLog, setEventLog] = useState([]);
  const [error, setError] = useState(null);
  const [appContext, setAppContext] = useState(null);

  const addEventToLog = useCallback((eventData) => {
    console.log("Chatwoot Event Received:", eventData);
    setEventLog(prevLog => [
      { timestamp: new Date().toISOString(), ...eventData },
      ...prevLog.slice(0, 19) // Keep last 20 events
    ]);
  }, []);

  // Helper function to validate JSON
  const isJSONValid = useCallback((str) => {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }, []);

  // Request data from Chatwoot on demand
  const requestChatwootData = useCallback(() => {
    try {
      window.parent.postMessage('chatwoot-dashboard-app:fetch-info', '*');
      addEventToLog({ event_name: 'chatwoot-dashboard-app:fetch-info', data: 'Requested data from Chatwoot' });
    } catch (e) {
      console.error("Error requesting data from Chatwoot:", e);
      addEventToLog({ event_name: 'chatwoot-dashboard-app:fetch-info.error', data: e.message });
    }
  }, [addEventToLog]);

  // Effect for Chatwoot Dashboard App message handling
  useEffect(() => {
    const handleChatwootMessage = (event) => {
      // Validate JSON data
      if (!isJSONValid(event.data)) {
        return;
      }

      const eventData = JSON.parse(event.data);
      addEventToLog({ event_name: 'message_received', data: eventData });

      // Handle appContext event from Chatwoot
      if (eventData.event === 'appContext') {
        console.log("Received appContext from Chatwoot:", eventData.data);
        setAppContext(eventData.data);
        setIsAppReady(true);

        // Extract data from the context
        if (eventData.data.conversation) {
          setCurrentConversation(eventData.data.conversation);
        }

        if (eventData.data.contact) {
          setContact(eventData.data.contact);
        }

        if (eventData.data.currentAgent) {
          setCurrentAgent(eventData.data.currentAgent);
        }

        addEventToLog({
          event_name: 'appContext_processed',
          data: {
            hasConversation: !!eventData.data.conversation,
            hasContact: !!eventData.data.contact,
            hasCurrentAgent: !!eventData.data.currentAgent
          }
        });
      }
    };

    // Listen for messages from Chatwoot
    window.addEventListener("message", handleChatwootMessage);

    // Request initial data from Chatwoot
    requestChatwootData();

    return () => {
      window.removeEventListener("message", handleChatwootMessage);
    };
  }, [addEventToLog, isJSONValid, requestChatwootData, setAppContext, setIsAppReady, setCurrentAgent]);

  // Handle refresh data button
  const handleRefreshData = useCallback(() => {
    requestChatwootData();
  }, [requestChatwootData]);

  // Load mock data for testing
  const loadMockData = useCallback(() => {
    const mockData = {
      conversation: {
        meta: {
          sender: {
            additional_attributes: {
              city: "",
              country: "",
              description: "",
              company_name: "",
              country_code: "",
              social_profiles: {
                instagram: "ahmed_magdy412"
              }
            },
            availability_status: "offline",
            email: null,
            id: 2,
            name: "Ahmed Magdy",
            phone_number: "+************",
            blocked: false,
            identifier: "<EMAIL>",
            thumbnail: "https://Chatwoot.lowcalories.ae/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBRdz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--b43adae1dfc2f43b40788dd9052359a363d2885b/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--1578d1a194762faed72c061c5d274341af8acd62/461244390_890953046575651_617710305352355255_n.jpg",
            custom_attributes: {},
            last_activity_at: **********,
            created_at: **********
          },
          channel: "Channel::Api",
          hmac_verified: false
        },
        id: 9,
        messages: [
          {
            id: 603,
            content: "who?",
            inbox_id: 1,
            conversation_id: 9,
            message_type: 0,
            content_type: "text",
            status: "sent",
            content_attributes: {},
            created_at: 1748778926,
            private: false,
            source_id: "WAID:3F86EF53B6AC736C7031",
            sender: {
              additional_attributes: {
                city: "",
                country: "",
                description: "",
                company_name: "",
                country_code: "",
                social_profiles: {
                  instagram: "ahmed_magdy412"
                }
              },
              custom_attributes: {},
              email: null,
              id: 2,
              identifier: "<EMAIL>",
              name: "Ahmed Magdy",
              phone_number: "+************",
              thumbnail: "https://Chatwoot.lowcalories.ae/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBRdz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--b43adae1dfc2f43b40788dd9052359a363d2885b/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--1578d1a194762faed72c061c5d274341af8acd62/461244390_890953046575651_617710305352355255_n.jpg",
              blocked: false,
              type: "contact"
            }
          },
          {
            id: 621,
            content: "Nice to meet you 😊",
            inbox_id: 1,
            conversation_id: 9,
            message_type: 0,
            content_type: "text",
            status: "sent",
            content_attributes: {},
            created_at: 1748779551,
            private: false,
            source_id: "WAID:F28ABB47725666D3029286C0467CDE35",
            sender: {
              additional_attributes: {
                city: "",
                country: "",
                description: "",
                company_name: "",
                country_code: "",
                social_profiles: {
                  instagram: "ahmed_magdy412"
                }
              },
              custom_attributes: {},
              email: null,
              id: 2,
              identifier: "<EMAIL>",
              name: "Ahmed Magdy",
              phone_number: "+************",
              thumbnail: "https://Chatwoot.lowcalories.ae/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBRdz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--b43adae1dfc2f43b40788dd9052359a363d2885b/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--1578d1a194762faed72c061c5d274341af8acd62/461244390_890953046575651_617710305352355255_n.jpg",
              blocked: false,
              type: "contact"
            }
          },
          {
            id: 628,
            content: "السلام عليكم",
            inbox_id: 1,
            conversation_id: 9,
            message_type: 0,
            content_type: "text",
            status: "sent",
            content_attributes: {},
            created_at: 1748780636,
            private: false,
            source_id: "WAID:5EA1FDAA5D07C45017E023FCA5949DA3",
            sender: {
              additional_attributes: {
                city: "",
                country: "",
                description: "",
                company_name: "",
                country_code: "",
                social_profiles: {
                  instagram: "ahmed_magdy412"
                }
              },
              custom_attributes: {},
              email: null,
              id: 2,
              identifier: "<EMAIL>",
              name: "Ahmed Magdy",
              phone_number: "+************",
              thumbnail: "https://Chatwoot.lowcalories.ae/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBRdz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--b43adae1dfc2f43b40788dd9052359a363d2885b/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--1578d1a194762faed72c061c5d274341af8acd62/461244390_890953046575651_617710305352355255_n.jpg",
              blocked: false,
              type: "contact"
            }
          }
        ],
        account_id: 1,
        uuid: "0b173d07-aeb1-4d62-b1e2-533d4a267df9",
        additional_attributes: {},
        agent_last_seen_at: **********,
        assignee_last_seen_at: 0,
        can_reply: true,
        contact_last_seen_at: **********,
        custom_attributes: {},
        inbox_id: 1,
        labels: [],
        muted: false,
        snoozed_until: null,
        status: "open",
        created_at: **********,
        updated_at: **********.319895,
        timestamp: **********,
        first_reply_created_at: **********,
        unread_count: 0
      },
      contact: {
        additional_attributes: {
          city: "",
          country: "",
          description: "",
          company_name: "",
          country_code: "",
          social_profiles: {
            instagram: "ahmed_magdy412"
          }
        },
        availability_status: "offline",
        email: null,
        id: 2,
        name: "Ahmed Magdy",
        phone_number: "+************",
        blocked: false,
        identifier: "<EMAIL>",
        thumbnail: "https://Chatwoot.lowcalories.ae/rails/active_storage/representations/redirect/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBRdz09IiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--b43adae1dfc2f43b40788dd9052359a363d2885b/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdCem9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjkwYjE5bWFXeHNXd2RwQWZvdyIsImV4cCI6bnVsbCwicHVyIjoidmFyaWF0aW9uIn19--1578d1a194762faed72c061c5d274341af8acd62/461244390_890953046575651_617710305352355255_n.jpg",
        custom_attributes: {},
        last_activity_at: **********,
        created_at: **********,
        contact_inboxes: [
          {
            inbox: {
              id: 1,
              avatar_url: "",
              channel_id: 1,
              name: "Demo Account",
              channel_type: "Channel::Api",
              provider: null
            },
            source_id: "fd7b01de-a193-4770-bced-652255f4e582"
          }
        ]
      },
      currentAgent: {
        id: 1,
        name: "Ahmed Magdy",
        email: "<EMAIL>"
      }
    };

    // Simulate receiving the data
    setAppContext(mockData);
    setIsAppReady(true);
    setCurrentConversation(mockData.conversation);
    setContact(mockData.contact);
    setCurrentAgent(mockData.currentAgent);

    addEventToLog({
      event_name: 'mock_data_loaded',
      data: 'Loaded real Chatwoot mock data for UI testing'
    });
  }, [addEventToLog]);

  // Dynamic body class for theme
  useEffect(() => {
    document.body.className = theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-100 text-black';
  }, [theme]);

  if (!isAppReady) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-800 text-white p-4">
        <div className="text-center">
          <svg className="animate-spin h-10 w-10 text-blue-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <h1 className="text-2xl font-semibold">Waiting for Chatwoot Dashboard App...</h1>
          <p className="text-gray-400 mt-2">This app needs to be loaded within Chatwoot as a Dashboard App.</p>
          <div className="mt-4 flex gap-3 justify-center">
            <button
              onClick={handleRefreshData}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
            >
              Request Data from Chatwoot
            </button>
            <button
              onClick={loadMockData}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
            >
              Load Mock Data
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 md:p-6 min-h-screen font-inter ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'}`}>
      <header className="mb-6">
        <h1 className={`text-3xl font-bold ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}>Chatwoot Dashboard App</h1>
        <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>Interacting with Chatwoot SDK (Theme: {theme})</p>
      </header>

      {error && (
        <div className="mb-4 p-3 bg-red-500 text-white rounded-md shadow">
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Messages Section - Full Width */}
      <div className={`mb-6 p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
        <h2 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Conversation Messages</h2>
        <Messages
          messages={currentConversation?.messages || []}
          theme={theme}
        />
      </div>

      {/* Data Details Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        <div className={`p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Current Agent</h2>
          {currentAgent ? <DetailRenderer title="Current Agent" data={currentAgent} theme={theme} /> : <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>Loading agent info...</p>}
        </div>

        <div className={`p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Conversation Details</h2>
          {currentConversation ? <DetailRenderer title="Current Conversation" data={currentConversation} theme={theme} /> : <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No active conversation or loading...</p>}
        </div>

        <div className={`p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Contact Details</h2>
          {contact ? <DetailRenderer title="Contact" data={contact} theme={theme} /> : <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No contact loaded or loading...</p>}
        </div>
      </div>

      <div className={`mt-6 p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
        <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Dashboard App Controls</h2>
        <div className="flex gap-4 flex-wrap">
          <button
            onClick={handleRefreshData}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-semibold transition-colors"
          >
            Refresh Data from Chatwoot
          </button>
          <button
            onClick={loadMockData}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md font-semibold transition-colors"
          >
            Load Mock Data
          </button>
          <button
            onClick={() => setError(null)}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md font-semibold transition-colors"
          >
            Clear Errors
          </button>
        </div>
        <div className="mt-4 text-sm text-gray-500">
          <p><strong>App Status:</strong> {isAppReady ? '✅ Connected to Chatwoot' : '⏳ Waiting for Chatwoot'}</p>
          <p><strong>Data Received:</strong> {appContext ? '✅ Yes' : '❌ No'}</p>
          <p><strong>Events Logged:</strong> {eventLog.length}</p>
        </div>
      </div>

      <div className={`mt-6 p-4 rounded-lg shadow ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'}`}>
        <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-blue-300' : 'text-blue-700'}`}>Event Log</h2>
        {eventLog.length === 0 ? (
          <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>No events captured yet.</p>
        ) : (
          <ul className="space-y-2 max-h-96 overflow-y-auto text-xs">
            {eventLog.map((log, index) => (
              <li key={index} className={`p-2 rounded ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <p className="font-mono">
                  <span className="font-semibold text-purple-400">{log.timestamp}: </span>
                  <span className="font-semibold text-green-400">{log.event_name}</span>
                </p>
                <pre className={`mt-1 p-1.5 rounded text-xs overflow-x-auto ${theme === 'dark' ? 'bg-gray-900 text-gray-300' : 'bg-gray-200 text-gray-700'}`}>
                  {JSON.stringify(log.data, null, 2)}
                </pre>
              </li>
            ))}
          </ul>
        )}
      </div>
       <footer className={`mt-8 text-center text-sm ${theme === 'dark' ? 'text-gray-500' : 'text-gray-500'}`}>
        <p>Chatwoot Helper - Dashboard App</p>
        <p>Integrated with Chatwoot using official Dashboard App API</p>
        <p>Listening for appContext events from Chatwoot parent window</p>
      </footer>
    </div>
  );
}

export default App;
